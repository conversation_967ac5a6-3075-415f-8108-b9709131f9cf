/**
 * Authentication Context
 *
 * This context provides authentication state and functions to the entire application.
 *
 * English: This file manages authentication state across the application
 * Tanglish: Indha file application-la authentication state-a manage pannum
 */

import { createContext, useState, useEffect, useContext } from 'react';
import { authService } from '../services/authService';
import config from '../config';
import axios from 'axios';

// Create the context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => {
  return useContext(AuthContext);
};

// Utility functions for session storage management
const authUtils = {
  /**
   * Get current user from session storage
   * @returns {Object|null} - Current user object or null if not logged in
   */
  getCurrentUser: () => {
    try {
      const userStr = sessionStorage.getItem(config.AUTH.USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing user data from session storage:', error);
      return null;
    }
  },

  /**
   * Get current authentication token
   * @returns {string|null} - Current token or null if not logged in
   */
  getToken: () => {
    return sessionStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  /**
   * Get main code from session storage
   * @returns {string|null} - Main code or null if not available
   */
  getmainCode: () => {
    return sessionStorage.getItem(config.AUTH.MAIN_CODE_KEY);
  },

  /**
   * Logout user and clear authentication data
   */
  logout: () => {
    // Clear all authentication data from session storage
    sessionStorage.removeItem(config.AUTH.TOKEN_KEY);
    sessionStorage.removeItem(config.AUTH.USER_KEY);
    sessionStorage.removeItem(config.AUTH.MAIN_CODE_KEY);
  },

  /**
   * Check if user is currently logged in
   * @returns {boolean} - True if user is logged in, false otherwise
   */
  isLoggedIn: () => {
    const token = authUtils.getToken();
    const user = authUtils.getCurrentUser();
    return !!(token && user);
  },

  /**
   * Verify the current token with the server
   * @returns {Promise} - Promise with verification result
   */
  verifyToken: async () => {
    try {
      const token = authUtils.getToken();
      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      const response = await axios.post(`${config.AUTHENTICATION_URL}/api/auth/verify`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      return { valid: true, user: response.data.user };
    } catch (error) {
      return {
        valid: false,
        error: error.response?.data?.error || 'Token verification failed'
      };
    }
  }
};

// Provider component
export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Load user from sessionStorage and verify token on initial render
  useEffect(() => {
    const loadUser = async () => {
      const user = authUtils.getCurrentUser();

      if (user) {
        // Verify the token is valid
        const verification = await authUtils.verifyToken();
        if (!verification.valid) {
          console.warn('Token verification failed:', verification.error);
          // Force logout if token is invalid
          authUtils.logout();
          setCurrentUser(null);
        } else {
          setCurrentUser(user);
        }
      }

      setLoading(false);
    };

    loadUser();
  }, []);

  /**
   * Login a user
   *
   * @param {string} username - User's username
   * @param {string} password - User's password
   * @param {string} mainCode - Optional main code for school-wise separation
   * @returns {Promise} - Promise with login result
   *
   * English: This function logs in a user and updates the context
   * Tanglish: Indha function user-a login panni context-a update pannum
   */
  const login = async (username, password, mainCode = null) => {
    try {
      setError('');

      // Prepare login data
      const loginData = { username, password };
      if (mainCode) {
        loginData.main_code = mainCode;
      }

      // Call the login service
      const response = await authService.userLoginService(loginData);
      const { token, user } = response.data;

      // Store authentication data in session storage
      sessionStorage.setItem(config.AUTH.TOKEN_KEY, token);
      sessionStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(user));

      // Store main_code if available (for Teachers and Admins)
      if (user.main_code) {
        sessionStorage.setItem(config.AUTH.MAIN_CODE_KEY, user.main_code);
      }

      // Update context state
      setCurrentUser(user);
      return response.data;
    } catch (error) {
      const errorMessage = error.response?.data?.error || error.error || 'Login failed';
      setError(errorMessage);
      throw error.response?.data || error;
    }
  };

  /**
   * Logout a user
   *
   * English: This function logs out a user and updates the context
   * Tanglish: Indha function user-a logout panni context-a update pannum
   */
  const logout = () => {
    authUtils.logout();
    setCurrentUser(null);
  };

  // Add a compatibility layer for any code that might be using schoolcode
  const compatibleCurrentUser = currentUser ? {
    ...currentUser,
    // Add schoolcode property that returns main_code for backward compatibility
    get maincode() {
      return this.main_code;
    }
  } : null;

  // Context value
  const value = {
    currentUser: compatibleCurrentUser,
    login,
    logout,
    error,
    loading,
    isLoggedIn: !!currentUser,
    // Add helper method to get school code
    getmainCode: () => authUtils.getmainCode()
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
